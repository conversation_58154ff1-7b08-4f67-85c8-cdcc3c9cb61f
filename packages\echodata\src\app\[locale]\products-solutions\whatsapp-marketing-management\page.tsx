'use client';
import type { Dictionary } from '@hi7/interface/dictionary';
import type { LocaleProps } from '@hi7/interface/i18n';
import { getDictionary } from '@hi7/lib/i18n';
import Hero from './<PERSON>';

import SwiperCore from 'swiper';
import 'swiper/css';
import 'swiper/css/scrollbar';
import { Mousewheel } from 'swiper/modules';
import { Swiper, SwiperSlide } from 'swiper/react';

SwiperCore.use([Mousewheel]);

async function page({ params }: LocaleProps) {
  const { locale } = params;
  const dictionary: Dictionary = await getDictionary(locale);

  return (
    <>
      <Swiper
        direction="vertical"
        cssMode={true}
        mousewheel={true}
        slidesPerView={1}
        scrollbar={{ draggable: true }}
        className="h-screen"
      >
        <SwiperSlide>
          <Hero dictionary={dictionary} locale={locale} />
        </SwiperSlide>
        <SwiperSlide>
          <Hero dictionary={dictionary} locale={locale} />
        </SwiperSlide>
        <SwiperSlide>
          <Hero dictionary={dictionary} locale={locale} />
        </SwiperSlide>
      </Swiper>
    </>
  );
}

export default page;
