import productBgCenter from '@hi7/assets/background/product-1-bg-1.png';
import productBgRight from '@hi7/assets/background/product-1-bg-2.png';
import ProductBgBottomLeft from '@hi7/assets/background/product-1-bg-bottom-left.svg';
import ProductBgBottomRight from '@hi7/assets/background/product-1-bg-bottom-right.svg';
import ProductBgTopLeft from '@hi7/assets/background/product-1-bg-top-left.svg';
import ProductBgTopRight from '@hi7/assets/background/product-1-bg-top-right.svg';
import Product1 from '@hi7/assets/icon/product-1-1.svg';
import Product2 from '@hi7/assets/icon/product-1-2.svg';
import Product3 from '@hi7/assets/icon/product-1-3.svg';
import Product4 from '@hi7/assets/icon/product-1-4.svg';

import TriggerAnimation from '@hi7/components/TriggerAnimation';
import type { Dictionary } from '@hi7/interface/dictionary';
import { LocaleProps } from '@hi7/interface/i18n';
import { getDictionary } from '@hi7/lib/i18n';
import Image from 'next/image';
import GetStarted from './GetStarted';

async function page({ params }: LocaleProps) {
  const { locale } = params;
  const dictionary: Dictionary = await getDictionary(locale);
  return (
    <>
      <div className="flex items-center justify-center overflow-hidden bg-linear-[180deg,#172DB1_0%,#90C6FF_50%,#E1EFFF_100%]">
        <div className="w-full">
          <div className="relative">
            <div className="lg:animate-product-1-top-ring-left absolute top-[10px] z-10 w-[60px] lg:top-0 lg:left-0 lg:z-0 lg:w-[198px]">
              <ProductBgTopLeft width="100%" />
            </div>
            <div className="lg:animate-product-1-top-ring-right absolute top-[50%] right-0 w-[100px] lg:top-[15%] lg:w-[165px]">
              <ProductBgTopRight width="100%" />
            </div>

            <div className="relative flex flex-col items-center justify-center px-3 py-11 lg:py-17">
              <div className="lg:animate-product-1-white-bg flex w-full rounded-[20px] bg-[#EEF5FF] px-4 py-9 lg:min-h-[535px] lg:rounded-[90px] lg:pr-0 lg:pl-10">
                <div className="lg:animate-product-1-white-content flex flex-1 flex-col justify-center text-center lg:text-left lg:opacity-0">
                  <h1
                    className="mr-[60px] mb-5 text-[40px] leading-[48px] font-bold lg:text-[50px] lg:leading-[55px]"
                    dangerouslySetInnerHTML={{
                      __html:
                        dictionary.productSolution.whatsAppMarketingManagement
                          .landing.title,
                    }}
                  />

                  <h3 className="text-[20px] leading-7">
                    {
                      dictionary.productSolution.whatsAppMarketingManagement
                        .landing.desc
                    }
                  </h3>
                </div>

                <div className="lg:animate-product-1-white-picture relative hidden w-full flex-[0.3] lg:block lg:translate-x-0 lg:opacity-0">
                  <div className="absolute right-[-80px] bottom-[50px]">
                    <Image
                      width={480}
                      height={320}
                      src={productBgRight}
                      alt={''}
                      // className="absolute"
                    />
                    <Image
                      width={480}
                      height={320}
                      src={productBgCenter}
                      alt={''}
                      className="absolute top-[-100px] right-[-90px]"
                    />
                  </div>
                </div>
              </div>
            </div>

            <div className="relative w-full pb-10 lg:hidden">
              <div className="pt-[40px]">
                <Image
                  width={360}
                  height={280}
                  src={productBgRight}
                  alt={''}
                  // className="absolute"
                />
                <Image
                  width={360}
                  height={280}
                  src={productBgCenter}
                  alt={''}
                  className="absolute right-0 bottom-[30%]"
                />
              </div>
            </div>
          </div>
          <TriggerAnimation>
            <div className="relative flex flex-col items-center justify-center px-5 lg:px-[250px] lg:pb-[160px]">
              <div className="lg:animate-product-1-bottom-ring-left absolute top-[-10%] left-[-35%] w-[194px] lg:top-[-25%] lg:left-0 lg:opacity-0">
                <ProductBgBottomLeft width="100%" />
              </div>
              <div className="lg:animate-product-1-bottom-ring-right absolute top-[55%] right-[-15%] w-[118px] lg:top-[25%] lg:right-0 lg:opacity-0">
                <ProductBgBottomRight width="100%" />
              </div>
              <h1 className="lg:animate-product-1-bottom-title mb-16 text-center text-[32px] leading-[38px] font-bold lg:text-[50px] lg:leading-[55px] lg:opacity-0">
                {
                  dictionary.productSolution.whatsAppMarketingManagement.feature
                    .title
                }
              </h1>
              <div className="lg:animate-product-1-bottom-content relative z-10 grid w-full grid-cols-1 justify-start gap-x-[120px] gap-y-8 pb-10 lg:grid-cols-2 lg:opacity-0">
                <div className="flex flex-col gap-3">
                  <Product1 />
                  <b className="text-[22px] leading-[26px]">
                    {
                      dictionary.productSolution.whatsAppMarketingManagement
                        .feature.feature1.title
                    }
                  </b>
                  <p className="text-[16px]">
                    {
                      dictionary.productSolution.whatsAppMarketingManagement
                        .feature.feature1.desc
                    }
                  </p>
                </div>
                <div className="flex flex-col gap-3">
                  <Product2 />
                  <b className="text-[22px] leading-[26px]">
                    {
                      dictionary.productSolution.whatsAppMarketingManagement
                        .feature.feature2.title
                    }
                  </b>
                  <p className="text-[16px]">
                    {
                      dictionary.productSolution.whatsAppMarketingManagement
                        .feature.feature2.desc
                    }
                  </p>
                </div>

                <div className="flex flex-col gap-3">
                  <Product3 />
                  <b className="text-[22px] leading-[26px]">
                    {
                      dictionary.productSolution.whatsAppMarketingManagement
                        .feature.feature3.title
                    }
                  </b>
                  <p className="text-[16px]">
                    {
                      dictionary.productSolution.whatsAppMarketingManagement
                        .feature.feature3.desc
                    }
                  </p>
                </div>

                <div className="flex flex-col gap-3">
                  <Product4 />
                  <b className="text-[22px] leading-[26px]">
                    {
                      dictionary.productSolution.whatsAppMarketingManagement
                        .feature.feature4.title
                    }
                  </b>
                  <p className="text-[16px]">
                    {
                      dictionary.productSolution.whatsAppMarketingManagement
                        .feature.feature4.desc
                    }
                  </p>
                </div>
              </div>
            </div>
          </TriggerAnimation>
        </div>
      </div>
      <GetStarted dictionary={dictionary} locale={locale} />
    </>
  );
}

export default page;
