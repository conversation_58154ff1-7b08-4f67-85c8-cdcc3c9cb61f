import type { Dictionary } from '@hi7/interface/dictionary';
import type { LocaleProps } from '@hi7/interface/i18n';
import { getDictionary } from '@hi7/lib/i18n';
import FullPageWrapper from './FullPageWrapper'; // make sure path matches
import <PERSON> from './<PERSON>';

async function page({ params }: LocaleProps) {
  const { locale } = params;
  const dictionary: Dictionary = await getDictionary(locale);

  return (
    <FullPageWrapper>
      <div className="section">
        <Hero dictionary={dictionary} locale={locale} />
      </div>
      <div className="section">
        <Hero dictionary={dictionary} locale={locale} />
      </div>
    </FullPageWrapper>
  );
}

export default page;
