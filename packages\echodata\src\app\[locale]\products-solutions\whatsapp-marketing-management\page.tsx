'use client';

import type { Dictionary } from '@hi7/interface/dictionary';
import type { LocaleProps } from '@hi7/interface/i18n';
import { getDictionary } from '@hi7/lib/i18n';
import { useEffect, useState } from 'react';
import Hero from './Hero';
import ScrollSnapContainer from './ScrollSnapContainer';

function Page({ params }: LocaleProps) {
  const { locale } = params;
  const [dictionary, setDictionary] = useState<Dictionary | null>(null);

  useEffect(() => {
    const loadDictionary = async () => {
      const dict = await getDictionary(locale);
      setDictionary(dict);
    };
    loadDictionary();
  }, [locale]);

  if (!dictionary) {
    return <div>Loading...</div>;
  }

  return (
    <ScrollSnapContainer snapType="mandatory">
      <Hero dictionary={dictionary} locale={locale} />
      <Hero dictionary={dictionary} locale={locale} />
      <Hero dictionary={dictionary} locale={locale} />
    </ScrollSnapContainer>
  );
}

export default Page;
