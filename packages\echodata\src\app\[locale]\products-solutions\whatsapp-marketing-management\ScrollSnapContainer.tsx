'use client';

import { useEffect } from 'react';
import './scroll-snap.css';

interface ScrollSnapContainerProps {
  children: React.ReactNode[];
  className?: string;
  snapType?: 'mandatory' | 'proximity';
}

export default function ScrollSnapContainer({
  children,
  className = '',
  snapType = 'mandatory',
}: ScrollSnapContainerProps) {
  useEffect(() => {
    // Apply scroll-snap to the body/html for full page snapping
    const originalBodyClass = document.body.className;
    const originalBodyStyle = document.body.style.cssText;
    const originalHtmlStyle = document.documentElement.style.cssText;

    // Add scroll-snap class and styles
    document.body.classList.add('scroll-snap-enabled');
    document.body.style.scrollSnapType = `y ${snapType}`;
    document.body.style.overflowY = 'scroll';
    document.documentElement.style.scrollBehavior = 'smooth';

    // Enhance scroll behavior for better snapping
    const handleWheel = (e: WheelEvent) => {
      // Optional: Add custom wheel handling for better snap behavior
      // This can help with more precise snapping on some browsers
    };

    document.addEventListener('wheel', handleWheel, { passive: true });

    return () => {
      // Cleanup
      document.body.className = originalBodyClass;
      document.body.style.cssText = originalBodyStyle;
      document.documentElement.style.cssText = originalHtmlStyle;
      document.removeEventListener('wheel', handleWheel);
    };
  }, [snapType]);

  return (
    <>
      {children.map((child, index) => (
        <section
          key={index}
          className={`scroll-snap-section ${className}`}
          style={{
            scrollSnapAlign: 'start',
            scrollSnapStop: 'always',
            minHeight: '100vh',
            display: 'flex',
            flexDirection: 'column',
            position: 'relative',
          }}
        >
          {child}
        </section>
      ))}
    </>
  );
}
