declare module 'fullpage.js' {
  interface FullPageOptions {
    responsiveWidth?: number;
    navigation?: boolean;
    scrollOverflow?: boolean;
    autoScrolling?: boolean;
    fitToSection?: boolean;
    anchors?: string[];
    menu?: string;
    sectionsColor?: string[];
    onLeave?: (index: number, nextIndex: number, direction: string) => void;
    afterLoad?: (anchorLink: string, index: number) => void;
    afterRender?: () => void;
    afterResize?: () => void;
    [key: string]: unknown;
  }

  interface FullPageInstance {
    destroy(type?: 'all'): void;
    moveTo(section: number | string, slide?: number | string): void;
    moveSectionUp(): void;
    moveSectionDown(): void;
    setAutoScrolling(active: boolean): void;
    setAllowScrolling(active: boolean): void;
    setKeyboardScrolling(active: boolean): void;
    reBuild(): void;
  }

  class FullPage {
    constructor(selector: string, options?: FullPageOptions);
    destroy(type?: 'all'): void;
    moveTo(section: number | string, slide?: number | string): void;
    moveSectionUp(): void;
    moveSectionDown(): void;
    setAutoScrolling(active: boolean): void;
    setAllowScrolling(active: boolean): void;
    setKeyboardScrolling(active: boolean): void;
    reBuild(): void;
  }

  export default FullPage;
}
