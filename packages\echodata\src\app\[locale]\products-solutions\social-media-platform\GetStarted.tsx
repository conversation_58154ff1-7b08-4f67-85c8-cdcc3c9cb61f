import type { DictionaryProps } from '@hi7/interface/i18n';
import { Locale } from '@hi7/lib/i18n';

interface GetStartedProps extends DictionaryProps {
  locale: Locale;
}

function GetStarted({ dictionary, locale }: GetStartedProps) {
  return (
    <div className="flex items-center justify-center overflow-hidden bg-linear-[97deg,#1093FF_-0.41%,#0506DC] text-white">
      <div className="m-auto w-full px-1 py-[60px] lg:max-w-[calc(100dvw_-_420px)]">
        <div className="relative">
          <div className="flex flex-col items-center justify-center">
            <h2 className="text-center text-[32px] font-bold lg:text-[50px] lg:leading-[60px]">
              {dictionary.productSolution.socialMediaPlatforms.tryForFree.title}
            </h2>
            {locale === 'zh' && (
              <h2 className="max-w-[150px] pt-5 text-center text-[12px] lg:max-w-[440px] lg:text-[20px] lg:leading-[30px]">
                {
                  dictionary.productSolution.socialMediaPlatforms.tryForFree
                    .desc
                }
              </h2>
            )}
          </div>
          <div className="mt-[30px] flex flex-col items-center justify-center">
            <a
              href="https://scrmchampion.com/contact"
              target="_blank"
              rel="noopener noreferrer"
              className="block w-max cursor-pointer rounded-[50px] bg-[#F5CC00] px-6 py-3 whitespace-nowrap text-[#1E1E1E] hover:bg-[#C7E5FF]"
            >
              {dictionary.general.freeTrial.button4}
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}

export default GetStarted;
