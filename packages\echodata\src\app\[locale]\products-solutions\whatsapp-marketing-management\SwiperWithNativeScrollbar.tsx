'use client';

import { useEffect, useRef, useState } from 'react';
import SwiperCore from 'swiper';
import 'swiper/css';
import { Mousewheel } from 'swiper/modules';
import { Swiper, SwiperSlide } from 'swiper/react';
import type { Swiper as SwiperType } from 'swiper/types';

SwiperCore.use([Mousewheel]);

interface SwiperWithNativeScrollbarProps {
  children: React.ReactNode[];
  className?: string;
}

export default function SwiperWithNativeScrollbar({
  children,
  className = 'h-screen',
}: SwiperWithNativeScrollbarProps) {
  const swiperRef = useRef<SwiperType | null>(null);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isScrolling, setIsScrolling] = useState(false);

  const totalSlides = children.length;
  const [virtualHeight, setVirtualHeight] = useState(0);

  // Set virtual height after component mounts
  useEffect(() => {
    if (typeof window !== 'undefined') {
      setVirtualHeight(totalSlides * window.innerHeight);

      // Ensure body can scroll
      document.body.style.overflow = 'auto';
      document.documentElement.style.overflow = 'auto';
    }

    return () => {
      // Cleanup
      if (typeof window !== 'undefined') {
        document.body.style.overflow = '';
        document.documentElement.style.overflow = '';
      }
    };
  }, [totalSlides]);

  useEffect(() => {
    if (!scrollContainerRef.current || virtualHeight === 0) return;

    const scrollContainer = scrollContainerRef.current;

    // Set the virtual scrollable height
    scrollContainer.style.height = `${virtualHeight}px`;

    const handleScroll = () => {
      if (isScrolling || !swiperRef.current || typeof window === 'undefined')
        return;

      const scrollTop = window.scrollY;
      const slideHeight = window.innerHeight;
      const targetSlide = Math.round(scrollTop / slideHeight);

      if (
        targetSlide !== currentSlide &&
        targetSlide >= 0 &&
        targetSlide < totalSlides
      ) {
        setIsScrolling(true);
        swiperRef.current.slideTo(targetSlide, 300);
        setCurrentSlide(targetSlide);

        // Reset scrolling flag after animation
        setTimeout(() => setIsScrolling(false), 350);
      }
    };

    // Sync browser scrollbar with current slide
    const syncScrollPosition = () => {
      if (isScrolling || typeof window === 'undefined') return;
      const targetScrollTop = currentSlide * window.innerHeight;
      window.scrollTo({ top: targetScrollTop, behavior: 'smooth' });
    };

    // Initial sync
    syncScrollPosition();

    window.addEventListener('scroll', handleScroll, { passive: true });

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [currentSlide, isScrolling, totalSlides, virtualHeight]);

  const handleSlideChange = (swiper: SwiperType) => {
    const newSlide = swiper.activeIndex;
    if (newSlide !== currentSlide && typeof window !== 'undefined') {
      setCurrentSlide(newSlide);
      setIsScrolling(true);

      // Sync browser scrollbar position
      const targetScrollTop = newSlide * window.innerHeight;
      window.scrollTo({ top: targetScrollTop, behavior: 'smooth' });

      setTimeout(() => setIsScrolling(false), 350);
    }
  };

  return (
    <>
      {/* Hidden scrollable container to create native scrollbar */}
      <div
        ref={scrollContainerRef}
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '1px',
          height: `${virtualHeight}px`,
          pointerEvents: 'none',
          opacity: 0,
          zIndex: -1,
        }}
      />

      {/* Swiper component */}
      <Swiper
        direction="vertical"
        mousewheel={{
          forceToAxis: true,
          sensitivity: 1,
          releaseOnEdges: true,
        }}
        slidesPerView={1}
        speed={300}
        allowTouchMove={true}
        className={className}
        onSwiper={(swiper) => {
          swiperRef.current = swiper;
        }}
        onSlideChange={handleSlideChange}
        style={{
          position: 'fixed',
          top: 0,
          left: 0,
          width: '100%',
          height: '100vh',
          zIndex: 1,
        }}
      >
        {children.map((child, index) => (
          <SwiperSlide key={index}>{child}</SwiperSlide>
        ))}
      </Swiper>
    </>
  );
}
