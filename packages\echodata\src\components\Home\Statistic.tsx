'use client';

import HomeBg2 from '@hi7/assets/background/home-bg-2.svg';
import type { DictionaryProps } from '@hi7/interface/i18n';
import TriggerAnimation from '../TriggerAnimation';

function Statistic({ dictionary }: DictionaryProps) {
  return (
    <TriggerAnimation>
      <div className="no-scrollbar overflow-x-auto overflow-y-hidden">
        <div className="flex min-w-[1440px] items-center justify-center bg-linear-[180deg,#C7E5FF_33.26%,#EDF7FF]">
          <div className="w-full max-w-[1440px] px-[84px]">
            <div className="relative min-h-[810px]">
              <div className="lg:animate-home-statistic-text-1 absolute top-0 left-[-4%] flex translate-y-[335px] flex-col items-center justify-center font-bold lg:translate-y-[-140px]">
                <h2 className="text-[74px] leading-[88.8px]">
                  {dictionary.home.statistic.statistic1.text1}
                </h2>
                <p className="text-[20px] leading-[24px] whitespace-nowrap text-[#100B69]">
                  {dictionary.home.statistic.statistic1.text2}
                </p>
                <p className="text-[20px] leading-[24px] whitespace-nowrap text-[#100B69]">
                  {dictionary.home.statistic.statistic1.text3}
                </p>
              </div>

              <div className="lg:animate-home-statistic-text-2 absolute top-0 left-[18%] flex translate-y-[385px] flex-col items-center justify-center font-bold lg:translate-y-[-160px]">
                <h2 className="text-[74px] leading-[88.8px] text-[#0A95FF]">
                  {dictionary.home.statistic.statistic2.text1}
                </h2>
                <p className="text-[20px] leading-[24px] whitespace-nowrap text-[#100B69]">
                  {dictionary.home.statistic.statistic2.text2}
                </p>
                <p className="text-[20px] leading-[24px] whitespace-nowrap text-[#100B69]">
                  {dictionary.home.statistic.statistic2.text3}
                </p>
              </div>

              <div className="lg:animate-home-statistic-text-3 absolute top-0 left-[43%] flex translate-y-[200px] flex-col items-center justify-center font-bold lg:translate-y-[-140px]">
                <h2 className="text-[74px] leading-[88.8px] font-bold text-[#100B69]">
                  {dictionary.home.statistic.statistic3.text1}
                </h2>
                <p className="text-[20px] leading-[24px] whitespace-nowrap text-[#100B69]">
                  {dictionary.home.statistic.statistic3.text2}
                </p>
                <p className="text-[20px] leading-[24px] whitespace-nowrap text-[#100B69]">
                  {dictionary.home.statistic.statistic3.text3}
                </p>
              </div>

              <div className="lg:animate-home-statistic-text-4 absolute top-0 left-[64%] flex translate-y-[385px] flex-col items-center justify-center font-bold lg:translate-y-[-160px]">
                <h2 className="text-[74px] leading-[88.8px] text-[#0A95FF]">
                  {dictionary.home.statistic.statistic4.text1}
                </h2>
                <p className="text-[20px] leading-[24px] whitespace-nowrap text-[#100B69]">
                  {dictionary.home.statistic.statistic4.text2}
                </p>
                <p className="text-[20px] leading-[24px] whitespace-nowrap text-[#100B69]">
                  {dictionary.home.statistic.statistic4.text3}
                </p>
              </div>

              <div className="lg:animate-home-statistic-text-5 absolute top-0 left-[88%] flex translate-y-[335px] flex-col items-center justify-center font-bold lg:translate-y-[-140px]">
                <h2 className="text-[74px] leading-[88.8px] font-bold text-[#100B69]">
                  {dictionary.home.statistic.statistic5.text1}
                </h2>
                <p className="text-[20px] leading-[24px] whitespace-nowrap text-[#100B69]">
                  {dictionary.home.statistic.statistic5.text2}
                </p>
                <p className="text-[20px] leading-[24px] whitespace-nowrap text-[#100B69]">
                  {dictionary.home.statistic.statistic5.text3}
                </p>
              </div>
              <div className="lg:animate-home-statistic-bg absolute right-0 bottom-0 left-0 lg:translate-y-[40%]">
                <HomeBg2 width="100%" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </TriggerAnimation>
  );
}

export default Statistic;
