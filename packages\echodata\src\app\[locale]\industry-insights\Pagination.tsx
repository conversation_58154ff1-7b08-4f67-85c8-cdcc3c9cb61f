import ChevronLeft from '@hi7/assets/icon/chevron-left.svg';
import ChevronRight from '@hi7/assets/icon/chevron-right.svg';
import { usePagination } from '@hi7/helpers/usePagination';
import useScreenSize from '@hi7/helpers/useScreenSize';
import { Dictionary } from '@hi7/interface/dictionary';
import clsx from 'clsx';

type PaginationProps = {
  totalPages: number;
  dictionary: Dictionary;
};

const BUTTON_STYLES = {
  active: 'bg-[#00B6FF] text-white',
  default:
    'rounded-md px-3 py-1 transition-colors lg:hover:bg-gray-100 w-8 flex justify-center',
};

const Pagination = ({ totalPages, dictionary }: PaginationProps) => {
  const {
    currentPage,
    itemsPerPageOptions,
    selectedItemsPerPage,
    handlePageChange,
    handleItemsPerPageChange,
  } = usePagination();

  const { isMobile } = useScreenSize();

  const renderPageButtons = () => {
    const buttons = [];
    const VISIBLE_PAGE = isMobile ? 4 : 5;
    const firstPage = 1;
    const lastPage = totalPages;
    const totalDisplayPages = Math.max(totalPages, firstPage);

    if (totalDisplayPages <= VISIBLE_PAGE) {
      for (let index = firstPage; index <= totalDisplayPages; index++) {
        buttons.push(
          <button
            key={index}
            onClick={() => handlePageChange(index)}
            className={clsx(
              BUTTON_STYLES.default,
              currentPage === index && BUTTON_STYLES.active,
            )}
          >
            {index}
          </button>,
        );
      }
    } else {
      if (currentPage < VISIBLE_PAGE) {
        for (let index = firstPage; index <= VISIBLE_PAGE - 1; index++) {
          buttons.push(
            <button
              key={index}
              onClick={() => handlePageChange(index)}
              className={clsx(
                BUTTON_STYLES.default,
                currentPage === index && BUTTON_STYLES.active,
              )}
            >
              {index}
            </button>,
          );
        }
        buttons.push(
          <span key="ellipsis" className="px-2 py-1 text-[#D9D9D9]">
            ...
          </span>,
        );
        buttons.push(
          <button
            key={lastPage}
            onClick={() => handlePageChange(lastPage)}
            className={clsx(
              BUTTON_STYLES.default,
              currentPage === lastPage && BUTTON_STYLES.active,
            )}
          >
            {lastPage}
          </button>,
        );
      } else if (currentPage >= totalDisplayPages - VISIBLE_PAGE + 1) {
        buttons.push(
          <button
            key={firstPage}
            onClick={() => handlePageChange(firstPage)}
            className={clsx(
              BUTTON_STYLES.default,
              currentPage === firstPage && BUTTON_STYLES.active,
            )}
          >
            {firstPage}
          </button>,
        );
        buttons.push(
          <span key="ellipsis" className="px-2 py-1 text-[#D9D9D9]">
            ...
          </span>,
        );
        for (
          let index = totalDisplayPages - VISIBLE_PAGE + 1;
          index <= lastPage;
          index++
        ) {
          buttons.push(
            <button
              key={index}
              onClick={() => handlePageChange(index)}
              className={clsx(
                BUTTON_STYLES.default,
                currentPage === index && BUTTON_STYLES.active,
              )}
            >
              {index}
            </button>,
          );
        }
      } else {
        buttons.push(
          <button
            key={firstPage}
            onClick={() => handlePageChange(firstPage)}
            className={clsx(
              BUTTON_STYLES.default,
              currentPage === firstPage && BUTTON_STYLES.active,
            )}
          >
            {firstPage}
          </button>,
        );
        buttons.push(
          <span key="ellipsis" className="px-2 py-1 text-[#D9D9D9]">
            ...
          </span>,
        );

        const start = Math.max(currentPage - 1, firstPage + 1);
        const end = Math.min(currentPage + 1, lastPage - 1);

        for (let index = start; index <= end; index++) {
          buttons.push(
            <button
              key={index}
              onClick={() => handlePageChange(index)}
              className={clsx(
                BUTTON_STYLES.default,
                currentPage === index && BUTTON_STYLES.active,
              )}
            >
              {index}
            </button>,
          );
        }

        buttons.push(
          <span key="ellipsis-end" className="px-2 py-1 text-[#D9D9D9]">
            ...
          </span>,
        );
        buttons.push(
          <button
            key={lastPage}
            onClick={() => handlePageChange(lastPage)}
            className={clsx(
              BUTTON_STYLES.default,
              currentPage === lastPage && BUTTON_STYLES.active,
            )}
          >
            {lastPage}
          </button>,
        );
      }
    }

    return buttons;
  };

  return (
    <div className="mt-5 flex w-full items-center justify-center space-x-1 p-4 text-sm">
      <button
        className={clsx(
          'rounded-md p-2 hover:bg-gray-100 disabled:opacity-50',
          { 'pointer-events-none opacity-50': currentPage === 1 },
        )}
        onClick={() => handlePageChange(currentPage - 1)}
        disabled={currentPage === 1}
      >
        <ChevronLeft className="h-5 w-5" />
      </button>

      <div className="flex items-center space-x-1">{renderPageButtons()}</div>

      <button
        className={clsx('rounded-md p-2 hover:bg-gray-100', {
          'pointer-events-none opacity-50': currentPage === totalPages,
        })}
        onClick={() => handlePageChange(currentPage + 1)}
        disabled={currentPage === totalPages}
      >
        <ChevronRight className="h-5 w-5" />
      </button>

      <select
        value={selectedItemsPerPage}
        onChange={(e) => handleItemsPerPageChange(Number(e.target.value))}
        className="ml-1 hidden items-center space-x-1 rounded-md border border-[#D9D9D9] px-2 py-1.5 hover:bg-gray-50 lg:flex"
      >
        {itemsPerPageOptions.map((option) => (
          <option key={option} value={option} className="text-sm">
            {option} / {dictionary.industryInsights.page}
          </option>
        ))}
      </select>
    </div>
  );
};

export default Pagination;
