import h1 from '@hi7/assets/logo/h1.png';
import h10 from '@hi7/assets/logo/h10.png';
import h11 from '@hi7/assets/logo/h11.png';
import h12 from '@hi7/assets/logo/h12.png';
import h13 from '@hi7/assets/logo/h13.png';
import h14 from '@hi7/assets/logo/h14.png';
import h15 from '@hi7/assets/logo/h15.png';
import h2 from '@hi7/assets/logo/h2.png';
import h3 from '@hi7/assets/logo/h3.png';
import h4 from '@hi7/assets/logo/h4.png';
import h5 from '@hi7/assets/logo/h5.png';
import h6 from '@hi7/assets/logo/h6.png';
import h7 from '@hi7/assets/logo/h7.png';
import h8 from '@hi7/assets/logo/h8.png';
import h9 from '@hi7/assets/logo/h9.png';
import type { Dictionary } from '@hi7/interface/dictionary';
import clsx from 'clsx';
import type { StaticImageData } from 'next/image';
import Image from 'next/image';

const PLATFORMS = [
  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  h7,
  h8,
  h9,
  h10,
  h11,
  h12,
  h13,
  h14,
  h15,
];

const InfiniteSliderItem = ({
  logo,
  index,
}: {
  logo: StaticImageData;
  index: number;
}) => {
  return (
    <div
      className="slide flex w-[95px] items-center justify-center lg:w-[125px]"
      key={index}
    >
      <div className="flex h-[65px] w-[65px] items-center justify-center overflow-hidden rounded-[10px] bg-white lg:h-[75px] lg:w-[75px]">
        <Image
          src={logo}
          alt="logo"
          className="h-[45px] w-[45px] object-contain"
          width={45}
          height={45}
        />
      </div>
    </div>
  );
};

const InfiniteSlider = ({
  location,
  dictionary,
}: {
  location: 'download' | 'home';
  dictionary: Dictionary;
}) => {
  return (
    <div className="m-auto overflow-hidden pb-[60px]">
      <h3
        className={clsx(
          'mb-10 text-center text-2xl',
          location === 'home' ? 'text-white' : 'text-black lg:text-white',
        )}
      >
        {dictionary.general.supportedPlatforms}
      </h3>
      <div className={`relative flex w-[calc(250px*${PLATFORMS.length * 2})]`}>
        <div
          className={`animate-infinite-slider flex w-[calc(250px*${PLATFORMS.length * 2})]`}
        >
          {PLATFORMS.map((logo, index) => (
            <InfiniteSliderItem logo={logo} index={index} key={index} />
          ))}
          {PLATFORMS.map((logo, index) => (
            <InfiniteSliderItem logo={logo} index={index} key={index} />
          ))}
        </div>
        <div
          className={`animate-infinite-slider flex w-[calc(250px*${PLATFORMS.length * 2})]`}
        >
          {PLATFORMS.map((logo, index) => (
            <InfiniteSliderItem logo={logo} index={index} key={index} />
          ))}
          {PLATFORMS.map((logo, index) => (
            <InfiniteSliderItem logo={logo} index={index} key={index} />
          ))}
        </div>
      </div>
    </div>
  );
};

export default InfiniteSlider;
