'use client';
import type { DictionaryProps } from '@hi7/interface/i18n';
import type { Locale } from '@hi7/lib/i18n';
import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

import Hero from './hero';
import ImageGrid from './imageGrid';
import Navbar from './navBar';
import SearchBar from './searchBar';

interface IndustryInsightsProps extends DictionaryProps {
  locale: Locale;
}

const IndustryInsights = ({ dictionary, locale }: IndustryInsightsProps) => {
  const searchParams = useSearchParams();
  const initialCategory = searchParams.get('category') || 'All Articles';

  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(12);
  const [search, setSearch] = useState('');
  const [selectedCategory, setSelectedCategory] = useState(initialCategory);

  // Convert navbar category name to ARTICLES object key
  const getCategoryKey = (categoryName: string) => {
    if (categoryName === 'All Articles') return 'all';

    // Map navbar categories to ARTICLES object keys
    const categoryMap: { [key: string]: string } = {
      'Customer Acquisition': 'CustomerAcquisition',
      'Account Data Filtering': 'AccountDataFiltering',
      'Mass Marketing': 'MassMarketing',
      'Social Media Marketing': 'SocialMediaMarketing',
    };

    return categoryMap[categoryName] || 'CustomerAcquisition'; // fallback
  };

  // Handle category change from navbar
  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category);
    setPage(1); // Reset to first page when category changes
  };

  // Reset to first page when search or page size changes
  useEffect(() => {
    setPage(1);
  }, [search, pageSize]);

  return (
    <div className="bg-[#e9f3ff]">
      <Hero dictionary={dictionary} />
      <Navbar
        onCategoryChange={handleCategoryChange}
        activeCategory={selectedCategory}
      />
      <SearchBar search={search} setSearch={setSearch} />
      <ImageGrid
        category={getCategoryKey(selectedCategory)}
        search={search}
        page={page}
        pageSize={pageSize}
        onPageChange={setPage}
        onPageSizeChange={setPageSize}
      />
    </div>
  );
};

export default IndustryInsights;
