'use client';
import type { DictionaryProps } from '@hi7/interface/i18n';
import type { Locale } from '@hi7/lib/i18n';
import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

import Hero from './hero';
import ImageGrid from './imageGrid';
import Navbar from './navBar';
import SearchBar from './searchBar';

interface IndustryInsightsProps extends DictionaryProps {
  locale: Locale;
}

const IndustryInsights = ({ dictionary, locale }: IndustryInsightsProps) => {
  const searchParams = useSearchParams();
  const category = (searchParams.get('category') || 'all').toLowerCase();

  // State for pagination and search
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(12);
  const [search, setSearch] = useState('');

  // Reset to first page when category, search, or page size changes
  useEffect(() => {
    setPage(1);
  }, [category, search, pageSize]);
  return (
    <div className="bg-[#e9f3ff]">
      <Hero dictionary={dictionary} />
      <Navbar />
      <SearchBar search={search} setSearch={setSearch} />
      <ImageGrid
        category={category}
        search={search}
        page={page}
        pageSize={pageSize}
        onPageChange={setPage}
        onPageSizeChange={setPageSize}
      />
    </div>
  );
};

export default IndustryInsights;
