'use client';

import ProductBgTopRight from '@hi7/assets/background/insight-bg-top-right.svg';
import productBgCenter from '@hi7/assets/background/insight-bg.png';

import Link from '@hi7/components/Link';
import type { DictionaryProps } from '@hi7/interface/i18n';
import { Locale } from '@hi7/lib/i18n';
import clsx from 'clsx';
import Image from 'next/image';
import { useState } from 'react';
import { ARTICLES, CATEGORIES, HEADLINES, TAGS } from './config';
import Pagination from './Pagination';

interface IndustryInsightsProps extends DictionaryProps {
  locale: Locale;
}

export default function IndustryInsights({
  dictionary,
  locale,
}: IndustryInsightsProps) {
  const [selected, setSelected] = useState([CATEGORIES[0]]);
  return (
    <>
      <div className="flex items-center justify-center">
        <div className="w-full">
          <div className="relative w-full">
            <div className="relative min-h-[250px] lg:min-h-[520px]">
              <Image
                fill
                src={productBgCenter}
                alt={''}
                className="object-cover"
              />
            </div>

            <div className="absolute top-0 right-0 bottom-0 hidden lg:block">
              <ProductBgTopRight height="100%" />
            </div>

            <div className="right- top-0 right-0 flex flex-col bg-[#1093FF] pt-5 text-[#100B69] lg:absolute lg:bg-transparent lg:pt-29 lg:pr-44">
              <div className="flex flex-col justify-center px-4 lg:max-w-[580px] lg:px-0">
                <h1 className="mb-5 text-[22px] leading-[26px] font-bold lg:text-[50px] lg:leading-[55px]">
                  B2B Influencer Marketing: Transforming Professional Engagement
                </h1>

                <h3 className="mb-5 lg:text-[20px] lg:leading-7">
                  In recent years, influencer marketing is now moving beyond
                  fashion, beauty, and lifestyle to gain a good..
                </h3>

                <Link
                  className="mb-5 lg:text-[20px] lg:leading-7"
                  url="/industry-insights/1"
                >
                  {dictionary.industryInsights.readMore}
                </Link>
              </div>
            </div>
          </div>

          <div className="relative m-auto grid gap-8 px-4 lg:min-h-[810px] lg:grid-cols-[1.8fr_1fr] lg:pr-26 lg:pl-30">
            <div className="lg:pt-[50px]">
              <div className="mb-5 flex max-w-[100vw] flex-row flex-nowrap gap-2.5 overflow-auto px-8 pt-5 pb-2.5 lg:flex-wrap lg:overflow-hidden lg:p-0">
                {CATEGORIES.map((pack) => (
                  <button
                    key={pack}
                    onClick={() => {
                      setSelected((prev) => {
                        if (prev.includes(pack)) {
                          return prev.filter((item) => item !== pack);
                        }
                        return [...prev, pack];
                      });
                    }}
                    className={clsx(
                      'cursor-pointer rounded-[25px] border-2 px-6 py-3 whitespace-nowrap capitalize',
                      selected.includes(pack)
                        ? 'border-[#F5CC00] bg-[#F5CC00] text-[#1E1E1E]'
                        : 'border-[#172DB1] bg-white text-[#172DB1]',
                    )}
                  >
                    {pack}
                  </button>
                ))}
              </div>
              <div className="flex flex-col gap-5 px-5">
                {ARTICLES.map(({ createdAt, description, image, title }) => (
                  <div className="grid grid-cols-[1fr] gap-5 lg:grid-cols-[325px_1fr]">
                    <div className="relative h-[228px] w-full lg:h-[212px] lg:w-[325px]">
                      <Image
                        src={image}
                        fill
                        alt={title}
                        className="rounded-[10px] object-cover"
                      />
                    </div>
                    <div className="flex flex-col gap-2.5">
                      <h2 className="text-[22px] font-bold">{title}</h2>
                      <span className="text-[#1E1E1E]/45">{createdAt}</span>
                      <p className="text-[#1E1E1E]">{description}</p>
                      <Link
                        className="cursor-pointer text-[#172DB1]"
                        url="/industry-insights/1"
                      >
                        {dictionary.industryInsights.readMore}
                      </Link>
                    </div>
                  </div>
                ))}
              </div>
              <Pagination totalPages={50} dictionary={dictionary} />
            </div>
            <div className="hidden bg-[#EDF7FF] px-8 pt-[50px] lg:block">
              <div className="max-w-[350px]">
                <h2 className="mb-4 text-[22px] font-bold text-[#100B69]">
                  {dictionary.industryInsights.popularHeadlines}
                </h2>
                <div className="mb-10 flex flex-col gap-4">
                  {HEADLINES.map((headline) => (
                    <p>{headline}</p>
                  ))}
                </div>
                <h2 className="mb-4 text-[22px] font-bold text-[#100B69]">
                  {dictionary.industryInsights.tag}
                </h2>
                <div className="flex flex-row flex-wrap gap-2.5">
                  {TAGS.map((headline) => (
                    <a className="rounded-[50px] border border-[#1E1E1E]/30 px-3 py-1">
                      {headline}
                    </a>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
