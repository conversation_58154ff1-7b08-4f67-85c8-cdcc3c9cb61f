'use client';

import type { Dictionary } from '@hi7/interface/dictionary';
import type { LocaleProps } from '@hi7/interface/i18n';
import { getDictionary } from '@hi7/lib/i18n';

async function page({ params }: LocaleProps) {
  const { locale } = params;
  const dictionary: Dictionary = await getDictionary(locale);

  return <h1> Hello world2</h1>;
  // return <ClientArticle dictionary={dictionary} locale={locale} />;
}

export default page;
