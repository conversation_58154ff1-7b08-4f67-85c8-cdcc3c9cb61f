import type { DictionaryProps } from '@hi7/interface/i18n';

function GetStarted({ dictionary }: DictionaryProps) {
  return (
    <div className="flex items-center justify-center overflow-hidden bg-linear-[97deg,#1093FF_-0.41%,#0506DC] text-white">
      <div className="w-full">
        <div className="relative min-h-[450px]">
          <div className="flex flex-col items-center justify-center pt-[84px] text-center">
            <h2 className="mb-2.5 text-[32px] leading-[38.4px] font-bold lg:text-[50px] lg:leading-[60px]">
              {dictionary.general.freeTrial.title}
            </h2>
            <p className="text-[14px] leading-[16px] lg:text-[20px] lg:leading-[28px]">
              {dictionary.general.freeTrial.desc}
            </p>
          </div>
          <div className="mt-8 flex flex-col items-center justify-center">
            <a
              href="https://scrmchampion.com/contact"
              target="_blank"
              rel="noopener noreferrer"
              className="mt-4 block w-max cursor-pointer rounded-[50px] bg-[#F5CC00] px-6 py-3 whitespace-nowrap text-[#1E1E1E] hover:bg-[#C7E5FF]"
            >
              {dictionary.general.freeTrial.button1}
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}

export default GetStarted;
