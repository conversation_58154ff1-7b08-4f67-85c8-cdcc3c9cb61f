{"name": "echodata", "version": "0.1.0", "private": true, "scripts": {"dev": "./auto/dev dev", "dev:stg": "./auto/dev stg", "dev:prod": "./auto/dev prod", "start": "node ./.next/standalone/packages/echodata/server.js", "build": "./auto/build dev", "build:stg": "./auto/build stg", "build:prod": "./auto/build prod", "lint": "echo lint"}, "devDependencies": {"@google-cloud/local-auth": "^3.0.1", "dotenv": "^16.4.5", "fs": "0.0.1-security", "googleapis": "^144.0.0", "wp-types": "^4.66.1", "xlsx": "^0.18.5"}, "dependencies": {"fullpage.js": "^3.1.2", "swiper": "^11.2.8"}}