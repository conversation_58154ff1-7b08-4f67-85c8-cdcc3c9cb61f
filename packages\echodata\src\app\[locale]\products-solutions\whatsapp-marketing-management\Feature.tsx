import tmp from '@hi7/assets/background/tmp.svg?url';
import type { DictionaryProps } from '@hi7/interface/i18n';
import type { Locale } from '@hi7/lib/i18n';
import { Arsenal } from 'next/font/google';
import Image from 'next/image';

interface FeatureProps extends DictionaryProps {
  locale: Locale;
}

const arsenal = Arsenal({
  subsets: ['latin'],
  weight: ['400', '700'],
  display: 'swap',
});

function Feature({ dictionary, locale }: FeatureProps) {
  const title =
    'Comprehensive\nMulti-Dimensional\nWhatsApp User and\nProfile Filtering';
  const TAGS = [
    'Active Accounts',
    'Banned Accounts',
    'Age',
    'Gender',
    'User Activity',
    'Hair Colour',
  ];

  return (
    <>
      <div className="h-screen w-full overflow-hidden rounded-tr-[10vw] rounded-br-[10vw] bg-[#047AFF]">
        <div className="mx-auto flex w-[88vw] justify-between">
          <div className="relative h-screen w-[36vw]">
            <Image fill src={tmp} alt="tmp" className="object-cover" priority />
          </div>
          <div className="mt-20 flex w-[60vw] flex-col items-end">
            <div
              className={`w-[52vw] border-b border-[#E9F3FF] pb-3 text-right text-[50px] leading-[110%] font-semibold whitespace-pre-line text-[#E9F3FF] ${arsenal.className} `}
            >
              {title}
            </div>
            <div className="mt-8 flex w-[45vw] flex-wrap justify-end gap-4 font-semibold">
              {TAGS.map((tag) => (
                <div className="flex h-[50px] items-center rounded-full bg-[#E9F3FF] px-5 text-[20px] text-[#047AFF]">
                  {tag}
                </div>
              ))}
              <div className="flex h-[50px] items-center rounded-full bg-[#E9F3FF] px-5 text-[20px] text-[#047AFF]">
                Facial Appearance&nbsp;
                <span className="font-thin"> (from Profile Picture) </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

export default Feature;
