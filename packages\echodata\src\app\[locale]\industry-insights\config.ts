import productBg1 from '@hi7/assets/background/insight-1.png';
import productBg2 from '@hi7/assets/background/insight-2.png';
import productBg3 from '@hi7/assets/background/insight-3.png';
import productBg4 from '@hi7/assets/background/insight-4.png';

export const CATEGORIES = [
  'All',
  'Cloud Control',
  'Account Nurturing',
  'Industry News and Trends',
  'Marketing Strategies',
  'Industry Tips',
];

export const ARTICLES = [
  {
    image: productBg1,
    title: 'B2B Influencer Marketing: Transforming Professional Engagement',
    description:
      'In recent years, influencer marketing is now moving beyond fashion, beauty, and lifestyle to gain a good..',
    createdAt: 'Apr 1, 2024',
  },
  {
    image: productBg2,
    title:
      'SCRM Champion Receives Prestigious Recognition for Its Cutting-Edge CRM Tools',
    description:
      'SCR<PERSON> Champion, a leader in customer relationship management solutions, recently gained a significant..',
    createdAt: 'Apr 1, 2024',
  },
  {
    image: productBg3,
    title: `Meta's Solution to Toxic Ad Interactions`,
    description:
      'Digital marketing is not stagnant. Businesses want to reach out and adhere to a professional online persona..',
    createdAt: 'Apr 1, 2024',
  },
  {
    image: productBg4,
    title: `Centralized Customer Conversations: Marketing Software for Unified Social Media Engagement`,
    description:
      'Managing customer interactions across multiple social media platforms can overwhelm businesses, leading to..',
    createdAt: 'Apr 1, 2024',
  },
] as const;

export const HEADLINES = [
  ...ARTICLES.map((s) => s.title),
  'Deck the Digital Halls: Mastering Holiday Shopping Campaigns',
];

export const TAGS = [
  '#b2b',
  '#influencer',
  '#marketing',
  '#champion',
  '#receives',
  '#scrm',
  '#whatsapp',
  '#telegram',
];
