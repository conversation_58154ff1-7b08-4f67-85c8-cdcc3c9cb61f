'use client';

import productBgCenter from '@hi7/assets/background/insight-bg.png';

import Link from '@hi7/components/Link';
import type { DictionaryProps } from '@hi7/interface/i18n';
import { Locale } from '@hi7/lib/i18n';
import Image from 'next/image';
import { useEffect, useRef, useState } from 'react';
import { ARTICLES, HEADLINES, TAGS } from '../config';

interface ClientArticleProps extends DictionaryProps {
  locale: Locale;
}

export default function ClientArticle({
  dictionary,
  locale,
}: ClientArticleProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const innerScrollRef = useRef<HTMLDivElement>(null);

  const [scrollPosition, setScrollPosition] = useState(0);

  useEffect(() => {
    const handleScroll = () => {
      if (containerRef.current) {
        const scrollLeft = containerRef.current.scrollLeft;
        setScrollPosition(scrollLeft);
      }
    };

    const container = containerRef.current;
    if (container) {
      container.addEventListener('scroll', handleScroll);
    }

    return () => {
      if (container) {
        container.removeEventListener('scroll', handleScroll);
      }
    };
  }, []);

  useEffect(() => {
    const indicator = innerScrollRef.current;
    if (indicator && containerRef.current) {
      const maxScroll =
        containerRef.current.scrollWidth - containerRef.current.clientWidth;
      const scrollPercentage = scrollPosition / maxScroll;
      indicator.style.transform = `translateX(${scrollPercentage * 64}px)`;
    }
  }, [scrollPosition]);

  return (
    <>
      <div className="flex items-center justify-center">
        <div className="w-full">
          <div className="relative w-full">
            <div className="relative min-h-[250px] lg:min-h-[520px]">
              <Image
                fill
                src={productBgCenter}
                alt={''}
                className="object-cover"
              />
              <div className="absolute top-0 right-0 bottom-0 left-0 hidden bg-[#000072]/40 lg:block"></div>
            </div>

            <div className="bottom-[40px] left-[130px] flex flex-col bg-[#000072] pt-5 text-white lg:absolute lg:bg-transparent">
              <div className="flex flex-col items-start justify-center gap-5 px-4 lg:max-w-[685px] lg:px-0">
                <span className="rounded-[25px] border-2 border-white px-6 py-3">
                  Cloud Control
                </span>
                <h1 className="text-[22px] leading-[26px] font-bold lg:text-[50px] lg:leading-[55px]">
                  B2B Influencer Marketing: Transforming Professional Engagement
                </h1>

                <p className="mb-5 text-sm">Apr 1, 2024</p>
              </div>
            </div>
          </div>

          <div className="relative m-auto grid max-w-[1440px] gap-8 lg:min-h-[810px] lg:grid-cols-[1.8fr_1fr]">
            <div className="p-5 lg:pt-[50px] lg:pl-[120px]">
              <div>
                <p className="mb-5">
                  In recent years, influencer marketing is now moving beyond
                  fashion, beauty, and lifestyle to gain a good foothold in the
                  B2B sector. B2B influencer marketing is emerging as a potent
                  solution to enhance trust, flood messages, and create
                  meaningful connections. With such a transformation is taking
                  place, let's see how it unfolds, along with some quality
                  examples that display the impact.
                </p>

                <h1 className="mb-5 text-[30px] font-bold">
                  The Rise of B2B Influencer Marketing
                </h1>

                <p className="mb-5">
                  B2B marketing is entirely distinct from B2C; it has
                  fundamentally different ways of reaching stakeholders who make
                  complex and, in most cases, high-stakes purchasing decisions.
                  Influencers in the B2B arena are often thought leaders,
                  industry experts, and analysts who typically amass large
                  audiences owing to their credibility and respect among their
                  niche. Partnering with such professionals allows brands to:
                </p>

                <ul className="mt-4 list-disc pl-5">
                  <li>Enhance their credibility and thought leadership.</li>
                  <li>Reach niche audiences effectively.</li>
                  <li>Provide educational and value-driven content.</li>
                </ul>

                <h1 className="mb-5 text-[30px] font-bold">
                  Key Drivers Behind the Trend
                </h1>
                <ol className="mt-4 mb-5 list-decimal pl-5">
                  <li>
                    <b>LinkedIn's Role as a B2B Hub :</b> Platforms such as
                    LinkedIn have taken center stage in B2B influencer
                    marketing, allowing professionals to share insights, hold
                    webinars, and promote sector events.
                  </li>
                  <li>
                    <b>Trust and Authority :</b> B2B buyers are more likely to
                    trust recommendations from recognized experts than direct
                    brand messaging. Influencers bridge this trust gap by
                    providing authentic endorsements.
                  </li>
                  <li>
                    <b>Complex Buying Cycles :</b> B2B buyers would be more
                    inclined to trust recommendations coming from recognized
                    experts than direct messages from a brand. This is where
                    influencers can help bridge that trust gap by providing
                    their authentic endorsement.
                  </li>
                </ol>

                <h1 className="mb-5 text-[30px] font-bold">
                  Quality Examples of B2B Influencer Marketing in Action
                </h1>
                <ol className="mt-4 mb-5 list-decimal pl-5">
                  <li>
                    <b>SAP's Collaboration with Tim Ferriss :</b> SAP, one of
                    the world's leading enterprise software companies, partnered
                    with author Tim Ferriss to create content about productivity
                    and innovation. Through podcasts and thought leadership
                    articles, Ferriss's ideas resonated with the target market
                    of SAP and made the company's solutions approachable and
                    usable.
                  </li>
                  <li>
                    <b>Adobe's Creative Cloud Campaign :</b> Adobe employed{' '}
                    <a
                      className="text-[#0506DC] underline"
                      href="https://google.com"
                    >
                      leading creative thinkers
                    </a>{' '}
                    to create content illustrating the capabilities of its
                    Creative Cloud solutions for business use. These influencers
                    created tutorials, case studies, and webinars that not only
                    demonstrated the features of the software but also showed
                    professional applications in areas from marketing to product
                    design.
                  </li>
                  <li>
                    <b>IBM and Tech Thought Leaders :</b> IBM's B2B influencer
                    strategy consisted of collaboration with tech thought
                    leaders to promote its AI and cloud computing solutions.
                    Influencers such as futurist Bernard Marr shared insights on
                    how IBM's technologies are driving innovation, reaching a
                    global audience of professionals and decision-makers.
                  </li>
                </ol>

                <h1 className="mb-5 text-[30px] font-bold">
                  Best Practices for Implementing B2B Influencer Marketing
                </h1>
                <ol className="mt-4 mb-5 list-decimal pl-5">
                  <li>
                    <b>Choose the Right Influencers :</b> Identify credible
                    professionals with a strong following in a particular
                    industry. Seek people whom your goals are aligned with those
                    of the brand.
                  </li>
                  <li>
                    <b>Focus on Education :</b> The B2B audiences seek quality
                    content that educates and provides solutions for problems.
                    The influencer can conduct webinars, whitepapers, case
                    studies, and in-depth guides with you.
                  </li>
                  <li>
                    <b>Leverage Multiple Channels :</b> Dispersing content
                    through channels like LinkedIn, YouTube, and niche forums
                    within your industry helps achieve maximum reach.
                  </li>
                  <li>
                    <b>Measure Impact :</b> Measure the progress of any planned
                    activity by checking the engagement generated, leads, and
                    brand sentiment.
                  </li>
                </ol>

                <p className="mb-5">
                  B2B influencer marketing is no longer just a supplementary
                  technique-it has become a linchpin of the strategy for
                  professional engagement in the reality of the present. By
                  collaborating with trusted voices, brands can build authority,
                  foster deeper connections with customers, and drive meaningful
                  business outcomes. As time goes on and the winds of change
                  continue to blow, brands adopting this road will furnish for
                  themselves a pedestal to be an opportune leader in market
                  spill-over exploitation.
                </p>

                <p className="mb-5">
                  <a
                    className="text-[#0506DC] underline"
                    href="https://google.com"
                  >
                    SCRM Champion
                  </a>{' '}
                  has solutions for private domain marketing and influencer
                  collaboration that help businesses navigate this scenario
                  deftly, from multi-platform account management to private
                  domain marketing and influencer collaboration. With its
                  expertise in digital strategy optimization, SCRM Champion
                  allows brands to operate B2B influencer marketing in a secure
                  and easy manner.
                </p>
              </div>

              <div className="my-9 flex flex-wrap gap-2.5">
                {[...TAGS, '#engagement'].map((headline) => (
                  <a className="rounded-[50px] border border-[#1E1E1E]/30 px-3 py-1">
                    {headline}
                  </a>
                ))}
              </div>
            </div>
            <div className="hidden bg-[#EDF7FF] px-8 pt-[50px] lg:block">
              <div className="max-w-[350px]">
                <h2 className="mb-4 text-[22px] font-bold text-[#100B69]">
                  {dictionary.industryInsights.popularHeadlines}
                </h2>
                <div className="mb-10 flex flex-col gap-4">
                  {HEADLINES.map((headline) => (
                    <p>{headline}</p>
                  ))}
                </div>
                <h2 className="mb-4 text-[22px] font-bold text-[#100B69]">
                  {dictionary.industryInsights.tag}
                </h2>
                <div className="flex flex-row flex-wrap gap-2.5">
                  {TAGS.map((headline) => (
                    <a className="rounded-[50px] border border-[#1E1E1E]/30 px-3 py-1">
                      {headline}
                    </a>
                  ))}
                </div>
              </div>
            </div>
          </div>
          <div className="m-auto max-w-[1440px] p-5 lg:px-[120px]">
            <h2 className="mb-5 text-3xl font-bold">
              {dictionary.industryInsights.relatedPost}
            </h2>

            <div className="relative h-[400px]">
              <div
                className="no-scrollbar absolute left-0 w-full overflow-x-auto lg:relative"
                ref={containerRef}
              >
                <div className="mb-[50px] grid w-[1100px] grid-cols-[minmax(390px,1fr)_minmax(390px,1fr)_minmax(390px,1fr)] gap-x-[40px] lg:w-full lg:auto-rows-[360px] lg:grid-cols-[360px_360px_360px] lg:gap-[60px]">
                  {ARTICLES.slice(1, 4).map(({ image, title }) => (
                    <Link
                      className="flex flex-col gap-5"
                      url="/industry-insights/1"
                    >
                      <div className="relative h-[235px] w-full">
                        <Image src={image} fill alt={title} />
                      </div>
                      <h3 className="text-[22px] leading-[26px] font-bold">
                        {title}
                      </h3>
                    </Link>
                  ))}
                </div>
              </div>
            </div>

            <div className="relative m-auto mt-8 h-[9px] w-[100px] rounded-2xl bg-[#D9D9D9]/80 lg:hidden">
              <div
                className="absolute left-0 h-[9px] w-[36px] rounded-2xl bg-[#1093FF] transition-all duration-300 ease-in-out"
                ref={innerScrollRef}
              ></div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
