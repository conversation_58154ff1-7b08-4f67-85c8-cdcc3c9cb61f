import homeBgCenter from '@hi7/assets/background/home-bg-center.png';
import homeBgLeft from '@hi7/assets/background/home-bg-left.png';
import homeBgRight from '@hi7/assets/background/home-bg-right.png';
import HomeBgRingLeft from '@hi7/assets/background/home-bg-ring-left.svg';
import HomeBgRingRight from '@hi7/assets/background/home-bg-ring-right.svg';
import SupportedPlatforms from '@hi7/components/SupportedPlatforms';

import h1 from '@hi7/assets/logo/h1.png';
import h12 from '@hi7/assets/logo/h12.png';
import h2 from '@hi7/assets/logo/h2.png';
import h3 from '@hi7/assets/logo/h3.png';
import h4 from '@hi7/assets/logo/h4.png';
import h6 from '@hi7/assets/logo/h6.png';
import h7 from '@hi7/assets/logo/h7.png';
import h8 from '@hi7/assets/logo/h8.png';
import h9 from '@hi7/assets/logo/h9.png';
import type { DictionaryProps } from '@hi7/interface/i18n';
import Image from 'next/image';

function Landing({ dictionary }: DictionaryProps) {
  return (
    <div className="flex items-center justify-center overflow-hidden bg-linear-[180deg,#000009_0%,#080CD5_35%]">
      <div className="w-full">
        <div className="lg:min-h-[810px]">
          <div className="flex flex-col items-center justify-center px-5 py-11 text-center text-white lg:py-17">
            <div className="lg:max-w-[850px]">
              <h1 className="mb-6 text-[40px] leading-[48px] font-bold lg:text-[54px] lg:leading-[64.8px]">
                {dictionary.home.landing.elevateBrand}
              </h1>
              <h3 className="text-[20px] leading-7">
                {dictionary.home.landing.amplifyReach}
              </h3>
            </div>
          </div>
          <div className="relative m-auto mb-[72px] flex h-[20dvh] w-[70dvw] items-center justify-center lg:hidden">
            <div className="relative h-[18dvh] w-[65dvw]">
              <Image
                fill
                src={homeBgRight}
                alt={''}
                className="animate-md-home-bg-right absolute bottom-0 object-contain"
              />

              <Image
                fill
                src={homeBgLeft}
                alt={''}
                className="animate-md-home-bg-left absolute bottom-0 object-contain"
              />
            </div>
            <Image
              fill
              src={homeBgCenter}
              alt={''}
              className="relative object-contain"
            />
          </div>
          <div className="relative hidden justify-center lg:flex">
            <div className="lg:animate-home-bg-ring absolute top-[-35%] left-0 opacity-0">
              <HomeBgRingLeft />
            </div>
            <div className="lg:animate-home-bg-ring absolute top-[-35%] right-0 opacity-0">
              <HomeBgRingRight />
            </div>
            <Image
              width={630}
              height={388}
              src={homeBgLeft}
              alt={''}
              className="lg:animate-home-bg-left absolute bottom-5"
            />
            <Image
              width={630}
              height={388}
              src={homeBgRight}
              alt={''}
              className="lg:animate-home-bg-right absolute bottom-5"
            />
            <Image
              width={854}
              height={483}
              src={homeBgCenter}
              alt={''}
              className="relative z-10"
            />

            <Image
              width={84}
              height={84}
              src={h1}
              alt={'Whatsapp'}
              className="lg:animate-home-h1 absolute top-[50%] right-[50%] rounded-full"
            />

            <Image
              width={84}
              height={84}
              src={h2}
              alt={'Telegram'}
              className="lg:animate-home-h2 absolute top-[50%] right-[50%] rounded-full"
            />
            <Image
              width={84}
              height={84}
              src={h3}
              alt={'Line'}
              className="lg:animate-home-h3 absolute top-[50%] right-[50%] rounded-full"
            />

            <Image
              width={30}
              height={30}
              src={h4}
              alt={'L'}
              className="lg:animate-home-h4 absolute top-[50%] right-[50%] rounded-full"
            />

            <Image
              width={30}
              height={30}
              src={h6}
              alt={'FB Messager'}
              className="lg:animate-home-h6 absolute top-[50%] right-[50%] rounded-full"
            />

            <Image
              width={30}
              height={30}
              src={h7}
              alt={'Meta'}
              className="lg:animate-home-h7 absolute top-[50%] right-[50%] rounded-full"
            />
            <Image
              width={20}
              height={20}
              src={h8}
              alt={'X'}
              className="lg:animate-home-h8 absolute top-[50%] right-[50%] rounded-full"
            />

            <Image
              width={68}
              height={68}
              src={h9}
              alt={'Instagram'}
              className="lg:animate-home-h9 absolute top-[50%] right-[50%] rounded-full"
            />

            <Image
              width={68}
              height={68}
              src={h12}
              alt={'Discord'}
              className="lg:animate-home-h12 absolute top-[50%] right-[50%] rounded-full"
            />
          </div>
        </div>
        <SupportedPlatforms location="home" dictionary={dictionary} />
      </div>
    </div>
  );
}

export default Landing;
