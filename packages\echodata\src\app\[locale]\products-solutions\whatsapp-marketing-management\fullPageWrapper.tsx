'use client';

import 'fullpage.js/dist/fullpage.css'; // Required CSS
import 'fullpage.js/dist/fullpage.extensions.min.js';
import { useEffect } from 'react';

interface FullPageWrapperProps {
  children: React.ReactNode;
}

export default function FullPageWrapper({ children }: FullPageWrapperProps) {
  useEffect(() => {
    let fullpageInstance: any = null;

    const initFullPage = async () => {
      if (typeof window !== 'undefined') {
        const fullpage = (await import('fullpage.js')).default;

        fullpageInstance = new fullpage('#fullpage', {
          responsiveWidth: 1024, // Enable only on desktop
          navigation: false,
          scrollOverflow: false,
          scrollBar: true,
        });
      }
    };

    initFullPage();

    return () => {
      if (fullpageInstance) {
        fullpageInstance.destroy('all');
      }
    };
  }, []);

  return <div id="fullpage">{children}</div>;
}
