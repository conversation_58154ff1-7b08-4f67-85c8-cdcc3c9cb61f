'use client';

import fullpage from 'fullpage.js';
import 'fullpage.js/dist/fullpage.css'; // Required CSS
import { useEffect } from 'react';

interface FullPageWrapperProps {
  children: React.ReactNode;
}

export default function FullPageWrapper({ children }: FullPageWrapperProps) {
  useEffect(() => {
    if (typeof window !== 'undefined') {
      new fullpage('#fullpage', {
        responsiveWidth: 1024, // Enable only on desktop
        navigation: true,
        scrollOverflow: true,
      });

      return () => {
        fullpage.destroy('all');
      };
    }
  }, []);

  return <div id="fullpage">{children}</div>;
}
