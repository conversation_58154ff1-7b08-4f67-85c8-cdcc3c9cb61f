'use client';
import { Heeb<PERSON> } from 'next/font/google';
import { CATEGORIES } from './config';

interface NavbarProps {
  onCategoryChange: (category: string) => void;
  activeCategory: string;
}

const heebo = Heebo({
  subsets: ['latin'],
  display: 'swap',
});

const Navbar = ({ onCategoryChange, activeCategory }: NavbarProps) => {
  const handleCategoryClick = (category: string) => {
    onCategoryChange(category);
  };

  return (
    <div className="mt-[3vh] flex w-full justify-center py-6 md:mt-[10vh]">
      <div className="flex h-[47px] w-[90%] flex-nowrap items-center justify-evenly gap-[5vw] overflow-hidden rounded-full bg-white px-6 py-1 shadow-lg/20 md:w-[65vw] md:gap-[2vw] 2xl:h-[68px]">
        {CATEGORIES.map((category) => (
          <button
            key={category}
            onClick={() => handleCategoryClick(category)}
            className={`relative cursor-pointer text-[18px] whitespace-nowrap transition-all duration-200 ease-in-out md:font-medium 2xl:text-base 2xl:text-[24px] ${heebo.className} ${
              activeCategory === category ? 'text-[#FF5542]' : 'text-[#047aff]'
            } `}
          >
            {category}
          </button>
        ))}
      </div>
    </div>
  );
};

export default Navbar;
