'use client';

import Logo from '@hi7/assets/logo/logo.svg';
import LocaleSwitcher from '@hi7/components/LocaleSwitcher';
import MenuLink from '@hi7/components/MenuLink';
import type { DictionaryProps } from '@hi7/interface/i18n';
import type { MenuLinkProps } from '@hi7/interface/link';
import clsx from 'clsx';
import Link from '../Link';
import P1 from './icons/p1.svg';
import P2 from './icons/p2.svg';
import P3 from './icons/p3.svg';
import P4 from './icons/p4.svg';
import P5 from './icons/p5.svg';
import P6 from './icons/p6.svg';

// type HeaderProps = Pick<i18n, 'dictionary'>;
// { dictionary }: HeaderProps
const Desktop = ({ dictionary }: DictionaryProps) => {
  const ROUTES: MenuLinkProps[] = [
    {
      url: '/products-solutions',
      children: dictionary.header.productSolution.title,
      items: [
        {
          icon: <P1 />,
          url: '/products-solutions/whatsapp-marketing-management',
          text: dictionary.header.productSolution.whatsAppMarketingManagement,
        },
        {
          icon: <P2 />,
          url: '/products-solutions/social-media-platform',
          text: dictionary.header.productSolution.socialMediaPlatforms,
        },
        {
          icon: <P3 />,
          url: '/products-solutions/customer-service-saas-tool',
          text: dictionary.header.productSolution.customerServicesSaasTool,
        },
        {
          icon: <P4 />,
          url: '/products-solutions/customer-management',
          text: dictionary.header.productSolution.customerManagement,
        },
      ],
    },
    {
      url: '/pricing',
      children: dictionary.header.pricing,
    },
    {
      url: '/industry-insights',
      children: dictionary.header.industryInsights,
    },
    {
      url: '/contact-us',
      children: dictionary.header.support.title,
      items: [
        {
          icon: <P5 />,
          url: '/contact-us',
          text: dictionary.header.support.contactUs,
        },
        {
          icon: <P6 />,
          url: '/help-center',
          text: dictionary.header.support.helpCentre,
        },
      ],
    },
    {
      url: '/download',
      children: dictionary.header.download,
    },
    {
      asButton: true,
      url: 'https://admin.scrmchampion.com/',
      children: `${dictionary.header.button.signUp}/${dictionary.header.button.logIn}`,
    },
  ];

  return (
    <>
      <div className="h-[78px]"></div>
      <nav
        className={clsx(
          'fixed top-0 z-30 flex h-[78px] w-screen min-w-screen transform items-center justify-between bg-linear-[124.78deg,#00B6FF_0.64%,#0506DC] px-30 py-4 text-white transition-all duration-200',
        )}
      >
        <div className={clsx('flex h-full')}>
          <Link url={''}>
            <Logo width="100%" height="100%" />
          </Link>
        </div>

        <div className="flex items-center gap-7.5 text-[14px] xl:text-[16px]">
          {ROUTES.map((route, index) => (
            <MenuLink key={index} {...route} />
          ))}

          <div className="z-50">
            <LocaleSwitcher />
          </div>
        </div>
      </nav>
    </>
  );
};

export default Desktop;
