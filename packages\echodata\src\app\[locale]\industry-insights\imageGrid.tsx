import human from '@hi7/assets/background/human.svg?url';
import unionLeft from '@hi7/assets/background/union-left.svg?url';
import Image from 'next/image';
import Link from 'next/link';
import { FaChevronRight } from 'react-icons/fa';
import Masonry from 'react-masonry-css';

import { ARTICLES } from './config';

interface ImageGridProps {
  category: string;
  search: string;
  page: number;
  pageSize: number;
  onPageChange: (page: number) => void;
  onPageSizeChange: (pageSize: number) => void;
}

export default function ImageGrid({
  category,
  search,
  page,
  pageSize,
  onPageChange,
  onPageSizeChange,
}: ImageGridProps) {
  // Filter articles based on category using the ARTICLES object structure
  const images =
    category === 'all'
      ? Object.values(ARTICLES).flat()
      : ARTICLES[category] || [];

  // Filter by search term (in title or desc, case insensitive)
  const filtered = images.filter(
    (item) =>
      item.title.toLowerCase().includes(search.toLowerCase()) ||
      item.description.toLowerCase().includes(search.toLowerCase()),
  );

  const total = filtered.length;
  const totalPages = Math.max(1, Math.ceil(total / pageSize));
  const start = (page - 1) * pageSize;
  const end = start + pageSize;
  const pageImages = filtered.slice(start, end);

  // Helper for page numbers
  const pageNumbers = [];
  for (let i = 1; i <= totalPages; i++) {
    pageNumbers.push(i);
  }

  // Masonry breakpoint configuration
  const breakpointColumnsObj = {
    default: 3,
    1100: 2,
    700: 1,
  };

  return (
    <div className="mx-[7vw]">
      <Masonry
        breakpointCols={breakpointColumnsObj}
        className="-ml-6 flex w-auto gap-[3vw]"
        columnClassName="pl-6 bg-clip-padding"
      >
        {pageImages.map((item, idx) => (
          <div
            key={idx}
            className="relative mb-16 flex break-inside-avoid flex-col"
          >
            {/* Top Section: Image Only */}
            <div className="overflow-hidden rounded-4xl shadow-sm">
              <div className="relative h-[20vh] md:h-[25vh]">
                <Image
                  src={item.image}
                  alt={item.title}
                  className="h-[20vh] w-full object-cover md:h-[30vh]"
                />
              </div>
            </div>

            {/* Bottom Section: Tags + Content */}
            <div className="mt-4 overflow-hidden rounded-4xl rounded-bl-none bg-white p-2 pb-0">
              {/* Tags Section */}
              <div className="pt-2 pl-4">
                <div className="flex flex-wrap gap-2">
                  {item.tags.split(', ').map((tag, tagIdx) => (
                    <span
                      key={tagIdx}
                      className="flex items-center gap-1 rounded-full bg-[#A6C4EE] px-2 py-1 text-xs font-medium text-[#04227D] 2xl:text-base"
                    >
                      {tagIdx === 0 && (
                        <Image
                          src={human}
                          alt="human icon"
                          width={14}
                          height={14}
                          className="flex-shrink-0"
                        />
                      )}
                      {tag}
                    </span>
                  ))}
                </div>
              </div>

              {/* Content Section */}
              <div className="border-gray-100 p-4 pt-3 pb-1 leading-tight text-[#04227D]">
                <h3 className="mb-2 text-base leading-none font-semibold md:text-[1.5vw]">
                  {item.title}
                </h3>
                <p className="mt-[2vh] mb-4 text-sm whitespace-pre-line md:text-[1vw]">
                  {item.description}
                </p>
              </div>
            </div>
            <div className="relative flex h-[7vh] justify-start">
              <Image
                src={unionLeft}
                alt="union-left"
                className="h-full w-[66%]"
              />

              <Link href={`/industry-insights/${item.id}`} passHref>
                <button className="absolute top-[20%] left-[65%] flex h-[70%] w-[35%] cursor-pointer items-center justify-center rounded-full bg-[#04227D] px-4 py-2 text-xs font-medium whitespace-nowrap text-white transition-colors hover:bg-blue-700 md:text-sm md:font-bold 2xl:text-xl">
                  <span className="inline-flex items-center">
                    Read More
                    <FaChevronRight className="ml-[2px] text-[0.8rem] text-white 2xl:text-[1.5rem]" />
                  </span>
                </button>
              </Link>
            </div>
            {/* Footer with Date and Read More */}
            <div className="absolute bottom-[3%] flex items-center justify-between pl-4">
              <span className="pl-2 text-sm font-bold text-[#FF5542] 2xl:text-lg">
                {item.createdAt} • 0 Read
              </span>
            </div>
          </div>
        ))}
      </Masonry>

      {/* Pagination Bar */}
      <div className="my-8 mt-0 flex flex-col-reverse items-center justify-center gap-6 md:flex-row">
        <div className="flex flex-wrap items-center justify-center gap-3">
          <button
            type="button"
            disabled={page <= 1}
            onClick={() => onPageChange(page - 1)}
            className="h-[2rem] w-[2rem] cursor-pointer rounded-3xl border border-gray-300 bg-white text-blue-500 shadow-sm disabled:cursor-default disabled:opacity-50"
          >
            &lt;
          </button>
          <div className="flex gap-1">
            {pageNumbers.map((num) => (
              <button
                key={num}
                type="button"
                className={`h-[2rem] w-[2rem] rounded-3xl border shadow-sm ${
                  page === num
                    ? 'border-[#FF5542] bg-[#FF5542] font-bold text-white'
                    : 'cursor-pointer border-gray-300 bg-white text-blue-500'
                }`}
                onClick={() => onPageChange(num)}
              >
                {num}
              </button>
            ))}
          </div>

          <button
            type="button"
            disabled={page >= totalPages}
            onClick={() => onPageChange(page + 1)}
            className="h-[2rem] w-[2rem] cursor-pointer rounded-3xl border border-gray-300 bg-white text-blue-500 shadow-sm disabled:cursor-default disabled:opacity-50"
          >
            &gt;
          </button>
        </div>
        {/* Page Size Selector */}
        <div className="md:ml-6">
          <label className="inline-flex items-center gap-2">
            <select
              value={pageSize}
              onChange={(e) => onPageSizeChange(Number(e.target.value))}
              className="h-[2rem] rounded-4xl bg-white px-2 py-1 text-blue-500 shadow-sm"
            >
              {[12, 24, 36, 48].map((size) => (
                <option key={size} value={size}>
                  {size} / page
                </option>
              ))}
            </select>
          </label>
        </div>
      </div>
    </div>
  );
}
