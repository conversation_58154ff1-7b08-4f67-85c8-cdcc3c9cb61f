import ProductBgBottomLeft from '@hi7/assets/background/product-2-bg-bottom-left.svg';
import ProductBgBottomRight from '@hi7/assets/background/product-2-bg-bottom-right.svg';
import ProductBgTopLeft from '@hi7/assets/background/product-2-bg-top-left.svg';
import ProductBgTopRight from '@hi7/assets/background/product-2-bg-top-right.svg';
import productBgCenter from '@hi7/assets/background/product-2-bg.png';
import Product1 from '@hi7/assets/icon/product-2-1.svg';
import Product2 from '@hi7/assets/icon/product-2-2.svg';
import Product3 from '@hi7/assets/icon/product-2-3.svg';

import TriggerAnimation from '@hi7/components/TriggerAnimation';
import type { Dictionary } from '@hi7/interface/dictionary';
import type { LocaleProps } from '@hi7/interface/i18n';
import { getDictionary } from '@hi7/lib/i18n';
import Image from 'next/image';
import GetStarted from './GetStarted';

async function page({ params }: LocaleProps) {
  const { locale } = params;
  const dictionary: Dictionary = await getDictionary(locale);
  return (
    <>
      <div className="flex items-center justify-center overflow-hidden bg-linear-[180deg,#172DB1_0%,#90C6FF_50%,#E1EFFF_100%] lg:overflow-y-auto">
        <div className="w-full">
          <div className="relative lg:min-h-dvh">
            <div className="lg:animate-product-2-bg-left absolute top-[-240px] left-[-200px] h-[80dvh] lg:top-[-18dvh] lg:left-0 lg:z-0 lg:h-[90dvh]">
              <ProductBgTopLeft height="100%" />
            </div>
            <div className="lg:animate-product-2-bg-right absolute top-[440px] right-0 h-[25dvh] lg:top-[-3dvh] lg:right-[-6dvw] lg:h-[90dvh] lg:w-[48dvw]">
              <ProductBgTopRight height="100%" />
            </div>

            <div className="relative flex w-full flex-col pt-20 text-[#231FC0] lg:flex-row lg:pt-[20dvh] lg:pl-[120px]">
              <div className="lg:animate-product-2-content flex flex-1 flex-col justify-center self-start px-9 text-center lg:translate-y-[-50%] lg:px-0 lg:text-left lg:opacity-0">
                <h1 className="mb-5 text-[40px] leading-[48px] font-bold lg:text-[50px] lg:leading-[55px]">
                  {
                    dictionary.productSolution.socialMediaPlatforms.landing
                      .title
                  }
                </h1>

                <h3 className="text-[20px] leading-7">
                  {dictionary.productSolution.socialMediaPlatforms.landing.desc}
                </h3>
              </div>

              <div className="lg:animate-product-2-picture relative mb-29 h-[40dvh] w-[98%] lg:my-0 lg:mt-18 lg:h-[40dvh] lg:w-[45dvw] lg:flex-[2]">
                <Image
                  fill
                  src={productBgCenter}
                  alt={''}
                  className="object-contain"
                />
              </div>
            </div>
          </div>
          <TriggerAnimation>
            <div className="relative flex flex-col items-center justify-center px-5 lg:px-[250px] lg:pb-[160px]">
              <div className="lg:animate-product-2-bottom-ring-left absolute top-[-10%] left-[-35%] hidden w-[444px] lg:top-[35%] lg:left-0 lg:block lg:opacity-0">
                <ProductBgBottomLeft width="100%" />
              </div>
              <div className="lg:animate-product-2-bottom-ring-right absolute top-[30%] right-[-60%] lg:top-[10%] lg:right-[-300px] lg:w-[468px] lg:opacity-0">
                <ProductBgBottomRight width="100%" />
              </div>
              <h1 className="lg:animate-product-2-bottom-title mb-16 text-center text-[32px] leading-[38px] font-bold lg:text-[50px] lg:leading-[55px] lg:opacity-0">
                {dictionary.productSolution.socialMediaPlatforms.feature.title}
              </h1>
              <div className="lg:animate-product-2-bottom-content relative z-10 grid w-full grid-cols-1 justify-start gap-x-[120px] gap-y-8 pb-10 lg:grid-cols-3 lg:opacity-0">
                <div className="flex flex-col gap-3">
                  <Product1 />
                  <b className="text-[22px] leading-[26px]">
                    {
                      dictionary.productSolution.socialMediaPlatforms.feature
                        .feature1.title
                    }
                  </b>
                  <p className="text-[16px]">
                    {
                      dictionary.productSolution.socialMediaPlatforms.feature
                        .feature1.desc
                    }
                  </p>
                </div>

                <div className="flex flex-col gap-3">
                  <Product2 />
                  <b className="text-[22px] leading-[26px]">
                    {
                      dictionary.productSolution.socialMediaPlatforms.feature
                        .feature2.title
                    }
                  </b>
                  <p className="text-[16px]">
                    {
                      dictionary.productSolution.socialMediaPlatforms.feature
                        .feature2.desc
                    }
                  </p>
                </div>

                <div className="flex flex-col gap-3">
                  <Product3 />
                  <b className="text-[22px] leading-[26px]">
                    {
                      dictionary.productSolution.socialMediaPlatforms.feature
                        .feature3.title
                    }
                  </b>
                  <p className="text-[16px]">
                    {
                      dictionary.productSolution.socialMediaPlatforms.feature
                        .feature3.desc
                    }
                  </p>
                </div>
              </div>
            </div>
          </TriggerAnimation>
        </div>
      </div>
      <GetStarted dictionary={dictionary} locale={locale} />
    </>
  );
}

export default page;
