'use client';

import industryInsights from '@hi7/assets/background/industry-insights.svg?url';
import Union from '@hi7/assets/background/Union.svg?url';
import type { DictionaryProps } from '@hi7/interface/i18n';
import clsx from 'clsx';
import Image from 'next/image';
import { useEffect, useRef, useState } from 'react';

const Landing = ({ dictionary }: DictionaryProps) => {
  const imageRef = useRef<HTMLDivElement>(null);
  const [buttonPosition, setButtonPosition] = useState({ bottom: '8px', left: '8px' });

  useEffect(() => {
    const updateButtonPosition = () => {
      if (imageRef.current) {
        const container = imageRef.current;
        const img = container.querySelector('img');
        if (img) {
          const containerRect = container.getBoundingClientRect();
          const imgRect = img.getBoundingClientRect();

          // Calculate position relative to actual image
          const leftOffset = imgRect.left - containerRect.left;
          const bottomOffset = containerRect.bottom - imgRect.bottom;

          setButtonPosition({
            left: `${leftOffset + 8}px`,
            bottom: `${bottomOffset + 8}px`
          });
        }
      }
    };

    updateButtonPosition();
    window.addEventListener('resize', updateButtonPosition);

    return () => window.removeEventListener('resize', updateButtonPosition);
  }, []);

  return (
    <div
      className={clsx(
        'h-[80vh]',
        'w-full',
        'bg-[#04227D]',
        'relative',
        'rounded-bl-[8vw]',
        'flex',
        'items-center',
        'justify-between',
      )}
    >
      {/* Left Section */}
      <div className="w-[41%] h-full flex items-center">
        <div className="flex flex-col gap-6 w-full">
          <h1 className="text-white text-[3.5vw] font-bold text-left">
            Industry Insights
          </h1>

          {/* Union Card with Data */}
          <div className="relative w-full h-[40vh] rounded-lg overflow-hidden">
            {/* Image wrapper to track actual image dimensions */}
            <div ref={imageRef} className="relative w-full h-full flex items-start justify-start">
              <Image src={Union}
                alt="Union"
                className="max-w-full max-h-full object-contain object-left masked-image"
              />
              {/* Button positioned relative to actual image */}
              <button
                className="
                  absolute
                  px-3 py-2
                  flex justify-center items-center
                  text-white font-bold text-xs
                  bg-[#FF5542] hover:bg-[#04217d] hover:text-[#FF5542]
                  transition duration-200
                  rounded-[20px]
                  z-10
                "
                style={{
                  bottom: buttonPosition.bottom,
                  left: buttonPosition.left,
                }}
              >
                Try Now for FREE!
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Right Section */}
      <div className="w-[54%] h-full flex items-center justify-end">
        <Image
          src={industryInsights}
          alt="industry-insights"
          className="w-full h-[60vh] object-cover object-left rounded-tl-[5vw] rounded-bl-[5vw]"
        />
      </div>
    </div>
  );
};

export default Landing;
