/* Scroll Snap Styles */

/* Smooth scrolling for the entire page */
html {
  scroll-behavior: smooth;
}

/* Apply scroll-snap to body when using scroll-snap container */
body.scroll-snap-enabled {
  scroll-snap-type: y mandatory;
  overflow-y: scroll;
  height: 100vh;
}

/* Individual scroll-snap sections */
.scroll-snap-section {
  scroll-snap-align: start;
  scroll-snap-stop: always;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* Ensure sections take full viewport height */
.scroll-snap-section > * {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* Optional: Add some visual feedback for scroll snap points */
.scroll-snap-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent 0%, rgba(0, 182, 255, 0.3) 50%, transparent 100%);
  z-index: 10;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.scroll-snap-section:target::before,
.scroll-snap-section.active::before {
  opacity: 1;
}

/* Enhance scrollbar appearance */
body::-webkit-scrollbar {
  width: 12px;
}

body::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 6px;
}

body::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #00B6FF 0%, #0506DC 100%);
  border-radius: 6px;
  border: 2px solid #f1f1f1;
}

body::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #0099CC 0%, #0404B8 100%);
}

/* Firefox scrollbar */
body {
  scrollbar-width: thin;
  scrollbar-color: #00B6FF #f1f1f1;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .scroll-snap-section {
    min-height: 100dvh; /* Use dynamic viewport height on mobile */
  }
  
  body::-webkit-scrollbar {
    width: 8px;
  }
}

/* Accessibility: Respect user's motion preferences */
@media (prefers-reduced-motion: reduce) {
  html {
    scroll-behavior: auto;
  }
  
  .scroll-snap-section::before {
    transition: none;
  }
}
