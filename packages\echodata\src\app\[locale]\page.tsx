import Feature1 from '@hi7/components/Home/Feature1';
import Feature2 from '@hi7/components/Home/Feature2';
import Feedback from '@hi7/components/Home/Feedback';
import GetStarted from '@hi7/components/Home/GetStarted';
import Help from '@hi7/components/Home/Help';
import Landing from '@hi7/components/Home/Landing';
import Statistic from '@hi7/components/Home/Statistic';
import { Dictionary } from '@hi7/interface/dictionary';
import { getDictionary, type Locale } from '@hi7/lib/i18n';

type PageProps = {
  params: {
    locale: Locale;
  };
};

export default async function Page({ params }: PageProps) {
  const { locale } = params;
  const t: Dictionary = await getDictionary(locale);

  return (
    <>
      <Landing dictionary={t} />
      <Statistic dictionary={t} />
      <Feature1 dictionary={t} />
      <Feature2 dictionary={t} />
      <Feedback dictionary={t} />
      <Help dictionary={t} />
      <GetStarted dictionary={t} />
    </>
  );
}
