import { Arsenal } from 'next/font/google';

const arsenal = Arsenal({
  subsets: ['latin'],
  weight: ['400', '700'],
  display: 'swap',
});

interface CTASectionProps {
  titleText?: string; // default: 'Scale Your Business to the Global Level Today'
  overlapMargin?: string; // default: -5vh
  height?: string; // default: 30vh
}

const CTASection: React.FC<CTASectionProps> = ({
  titleText = 'Scale Your Business to the Global Level Today',
  overlapMargin = '-5vh',
  height = '45vh',
}) => {
  return (
    <div
      className={`relative z-10 bg-gradient-to-b from-transparent to-[#04227D] to-80% md:to-50%`}
      style={{ marginTop: overlapMargin }}
    >
      <div className="pointer-events-none relative w-full" style={{ height }} />

      <div className="relative px-8 pb-13 text-center text-white 2xl:pb-18">
        <h2
          className={`${arsenal.className} text-[11vw] leading-none font-bold md:text-[4.3vw]`}
        >
          {titleText}
        </h2>

        <div className="mt-8 flex flex-col items-center justify-center gap-3 md:mt-4 md:flex-row">
          <button className="w-[155px] cursor-pointer rounded-4xl bg-[#FF5542] py-2 text-sm font-semibold text-white transition duration-200 hover:bg-white hover:text-[#FF5542] 2xl:w-[200px] 2xl:py-3 2xl:text-lg">
            Try Now for FREE!
          </button>
          <button className="w-[155px] cursor-pointer rounded-4xl bg-[#047AFF] py-2 text-sm font-semibold text-white transition duration-200 hover:bg-white hover:text-[#FF5542] 2xl:w-[200px] 2xl:py-3 2xl:text-lg">
            Discover More
          </button>
        </div>
      </div>
    </div>
  );
};

export default CTASection;
