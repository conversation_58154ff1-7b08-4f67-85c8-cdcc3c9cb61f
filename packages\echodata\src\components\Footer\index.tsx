'use client';

import Facebook from '@hi7/assets/icon/facebook.svg';
import Instagram from '@hi7/assets/icon/instagram.svg';
import LinkedIn from '@hi7/assets/icon/linkedin.svg';
import X from '@hi7/assets/icon/x.svg';
import GetStarted1 from '@hi7/assets/logo/get-started-1.svg';
import GetStarted2 from '@hi7/assets/logo/get-started-2.svg';
import GetStarted3 from '@hi7/assets/logo/get-started-3.svg';
import Logo from '@hi7/assets/logo/logo.svg';
import type { Dictionary } from '@hi7/interface/dictionary';
import type { MediaProps, MenuLinkProps } from '@hi7/interface/link';
import { type Locale } from '@hi7/lib/i18n';
import clsx from 'clsx';
import { usePathname } from 'next/navigation';
import Link from '../Link';

type FooterProps = {
  dictionary: Dictionary;
  currentLocale: Locale;
};

const Footer = ({ dictionary }: FooterProps) => {
  const pathname = usePathname();

  const showReward = pathname.split('/').length === 2; // is home page

  const links: MenuLinkProps[] = [
    {
      url: '/products-solutions/whatsapp-marketing-management',
      children: dictionary.footer.whatsappMarkting,
    },
    {
      url: '/products-solutions/social-media-platform',
      children: dictionary.footer.socialMarkting,
    },
    {
      url: '/products-solutions/customer-service-saas-tool',
      children: dictionary.footer.saasTool,
    },
    {
      url: '/products-solutions/customer-management',
      children: dictionary.footer.customerManagement,
    },
  ];

  const services: MenuLinkProps[] = [
    { url: '/industry-insights', children: dictionary.footer.industryInsights },
    { url: '/pricing', children: dictionary.footer.productPrice },
    { url: '/contact-us', children: dictionary.footer.contactUs },
    { url: '/help-center', children: dictionary.footer.helpCentre },
  ];

  // const products: LinkProps[] = [
  //   {
  //     url: `${'ctrlfire' as ProductSectionId}`,
  //     children: dictionary.footer.ctrlfire,
  //   },
  //   {
  //     url: `${'scrm' as ProductSectionId}`,
  //     children: dictionary.footer.scrm,
  //   },
  //   {
  //     url: `${'cloudseven' as ProductSectionId}`,
  //     children: dictionary.footer.cloudseven,
  //   },
  //   {
  //     url: `${'picasso' as ProductSectionId}`,
  //     children: dictionary.footer.picasso,
  //   },
  //   {
  //     url: `${'imx' as ProductSectionId}`,
  //     children: dictionary.footer.imx,
  //   },
  //   {
  //     url: `${'elfproxy' as ProductSectionId}`,
  //     children: dictionary.footer.eifproxy,
  //   },
  //   {
  //     url: `${'echodata' as ProductSectionId}`,
  //     children: dictionary.footer.echodata,
  //   },
  //   {
  //     url: `${'workgram' as ProductSectionId}`,
  //     children: dictionary.footer.workgram,
  //   },
  // ];

  const medias: MediaProps[] = [
    {
      icon: <Facebook />,
      url: 'https://www.facebook.com/profile.php?id=61566180207939',
      target: '_blank',
    },
    {
      icon: <Instagram />,
      url: 'https://www.instagram.com/hisevendm/',
      target: '_blank',
    },
    {
      icon: <X width="21px" />,
      url: 'https://www.x.com/hisevendm/',
      target: '_blank',
    },
    {
      icon: <LinkedIn />,
      url: 'https://my.linkedin.com/company/hiseven?trk=public_post_feed-actor-name',
      target: '_blank',
    },
  ];

  const tnc: MenuLinkProps[] = [
    {
      url: '/privacy',
      target: '_blank',
      children: dictionary.footer.privacy,
    },
    {
      url: '/terms',
      target: '_blank',
      children: dictionary.footer.terms,
    },
  ];

  return (
    <div
      className={clsx('lg:snap-start', 'bg-[#100B69] text-white', 'relative')}
    >
      {showReward && (
        <div className="absolute top-[-70px] left-[50%] flex w-[calc(100%-64px)] translate-x-[-50%] flex-col items-center justify-center gap-4 rounded-[20px] bg-white p-5 lg:h-[140px] lg:w-[680px] lg:flex-row lg:gap-0 lg:px-[60px] lg:py-[30px]">
          <div className="max-w-[165px]">
            <GetStarted1 width="100%" />
          </div>

          <div className="mt-2.5 flex flex-row items-end justify-center gap-2 lg:mt-0 lg:mr-2.5 lg:ml-[52px]">
            <div className="-mb-0.5 w-[25%] lg:max-w-[90px]">
              <GetStarted2 width="100%" />
            </div>
            <div className="w-[50%] lg:max-w-[245px]">
              <GetStarted3 width="100%" />
            </div>
          </div>
        </div>
      )}

      <div className="mx-auto px-4 pt-[120px] pb-[60px] text-[14px] leading-[20px] lg:px-30">
        <div className="grid grid-cols-1 gap-7">
          {/** First Row */}
          <div className="flex flex-col gap-5 lg:flex-row lg:gap-[50px]">
            <div className="flex flex-col items-start gap-5 lg:flex-[0.6]">
              <Logo height={36} />

              <span>{dictionary.footer.overview}</span>

              <div className="hidden auto-rows-[24px] grid-cols-[24px_24px_24px_24px] items-center gap-4 lg:grid">
                {medias.map((media, index) => (
                  <Link key={index} url={media.url} target={media.target}>
                    <li className="flex items-center">{media.icon}</li>
                  </Link>
                ))}
              </div>
            </div>

            <div className="flex-[0.55]"></div>
            <div className="flex flex-1 flex-col gap-5 lg:flex-row lg:gap-[50px]">
              <div className="flex min-w-28 flex-[1_0_0] flex-col gap-[10px] lg:max-w-[200px]">
                <h6 className="font-bold">
                  {dictionary.footer.productSolution}
                </h6>

                <ul className="flex flex-col gap-[10px]">
                  {links.map((link, index) => (
                    <li key={index}>
                      <Link
                        url={link.url}
                        target={link.target}
                        className="text-inherit"
                      >
                        {link.children}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>

              <div className="flex max-w-[150px] min-w-28 flex-[1_0_0] flex-col gap-[10px]">
                <h6 className="text-[16px] font-bold">
                  {dictionary.footer.quickLinks}
                </h6>

                <ul className="flex flex-col gap-[10px]">
                  {services.map((service, index) => (
                    <li key={index}>
                      <Link
                        url={`/service/${service.url}`}
                        target={service.target}
                        className="text-inherit"
                      >
                        {service.children}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>

              <div className="flex min-w-20 flex-[1_0_0] flex-col gap-[10px]">
                <h6 className="text-[16px] font-bold">
                  {dictionary.footer.support}
                </h6>

                <ul className="flex flex-col gap-[10px]">
                  <li>
                    {dictionary.footer.email.title}:{' '}
                    <a
                      className="text-[#1093FF]"
                      href="mailto:<EMAIL>"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <EMAIL>
                    </a>
                  </li>
                  <li>
                    {dictionary.footer.customerService.title}:{' '}
                    <a
                      className="text-[#1093FF]"
                      href={dictionary.footer.customerService.url}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      {dictionary.footer.customerService.desc}
                    </a>
                  </li>
                  <li>
                    {dictionary.footer.feedback.title}:{' '}
                    <a
                      className="text-[#1093FF]"
                      href="https://t.me/vonkinbot?start=0_0_8"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      https://t.me/vonkinbot
                    </a>
                  </li>
                </ul>
              </div>

              <div className="grid auto-rows-[24px] grid-cols-[24px_24px_24px_24px] items-center gap-4 lg:hidden">
                {medias.map((media, index) => (
                  <Link key={index} url={media.url} target={media.target}>
                    <li className="flex items-center">{media.icon}</li>
                  </Link>
                ))}
              </div>
            </div>
          </div>

          {/** Second Row */}
          <div className="lg:text flex flex-col gap-6 text-[14px] leading-normal text-[#8785B4] lg:flex-row lg:items-center lg:justify-between lg:text-center">
            <span>{dictionary.footer.copyright}</span>

            <div className="flex gap-6">
              {tnc.map((route, index) => (
                <Link key={index} url={route.url} target={route.target}>
                  {route.children}
                </Link>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Footer;
